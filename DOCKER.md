# Docker Setup for Presentation AI

This guide explains how to run the Presentation AI application using <PERSON><PERSON> and <PERSON><PERSON>.

## Prerequisites

- <PERSON><PERSON> or <PERSON><PERSON> installed on your system
- Docker Compose (or podman-compose)

## Quick Start

1. **Copy the environment file:**
   ```bash
   cp .env.example .env
   ```

2. **Edit the `.env` file with your actual API keys and secrets:**
   - Set your `OPENAI_API_KEY`
   - Set your `TOGETHER_AI_API_KEY`
   - Set your Google OAuth credentials (`GOOGLE_CLIENT_ID` and `GOOGLE_CLIENT_SECRET`)
   - Generate a random string for `NEXTAUTH_SECRET`

3. **Build and run with Docker Compose:**
   ```bash
   docker-compose up --build
   ```

   **Or with <PERSON><PERSON>:**
   ```bash
   podman-compose up --build
   ```

4. **Access the application:**
   - Open your browser and go to `http://localhost:3000`
   - The PostgreSQL database will be available on `localhost:5432`

## Manual Docker Commands

If you prefer to run containers manually:

1. **Create a network:**
   ```bash
   docker network create presentation-ai-network
   # or with podman:
   podman network create presentation-ai-network
   ```

2. **Run PostgreSQL:**
   ```bash
   docker run -d \
     --name presentation-ai-db \
     --network presentation-ai-network \
     -e POSTGRES_DB=presentation_ai \
     -e POSTGRES_USER=postgres \
     -e POSTGRES_PASSWORD=postgres \
     -p 5432:5432 \
     postgres:15-alpine
   
   # or with podman:
   podman run -d \
     --name presentation-ai-db \
     --network presentation-ai-network \
     -e POSTGRES_DB=presentation_ai \
     -e POSTGRES_USER=postgres \
     -e POSTGRES_PASSWORD=postgres \
     -p 5432:5432 \
     postgres:15-alpine
   ```

3. **Build the application image:**
   ```bash
   docker build -t presentation-ai .
   # or with podman:
   podman build -t presentation-ai .
   ```

4. **Run the application:**
   ```bash
   docker run -d \
     --name presentation-ai-app \
     --network presentation-ai-network \
     -p 3000:3000 \
     --env-file .env \
     -e DATABASE_URL="******************************************************/presentation_ai" \
     presentation-ai
   
   # or with podman:
   podman run -d \
     --name presentation-ai-app \
     --network presentation-ai-network \
     -p 3000:3000 \
     --env-file .env \
     -e DATABASE_URL="******************************************************/presentation_ai" \
     presentation-ai
   ```

## Environment Variables

The application requires the following environment variables:

- `DATABASE_URL`: PostgreSQL connection string
- `NEXTAUTH_URL`: Your application URL (e.g., http://localhost:3000)
- `NEXTAUTH_SECRET`: Random secret for NextAuth
- `OPENAI_API_KEY`: Your OpenAI API key
- `TOGETHER_AI_API_KEY`: Your Together AI API key
- `GOOGLE_CLIENT_ID`: Google OAuth client ID
- `GOOGLE_CLIENT_SECRET`: Google OAuth client secret

## Development vs Production

### Development
- Use `docker-compose.yml` as provided
- Set `NODE_ENV=development` in your `.env` file
- Database migrations will run automatically

### Production
- Update environment variables in `docker-compose.yml`
- Set `NODE_ENV=production`
- Consider using Docker secrets for sensitive data
- Use a managed database service instead of the containerized PostgreSQL

## Troubleshooting

1. **Port conflicts:** If port 3000 or 5432 are already in use, change the ports in `docker-compose.yml`

2. **Database connection issues:** Ensure the PostgreSQL container is healthy before the app starts

3. **Environment variables:** Make sure all required environment variables are set in your `.env` file

4. **Prisma issues:** The container automatically runs `prisma db push` to sync the database schema

## Stopping the Application

```bash
docker-compose down
# or with podman:
podman-compose down
```

To also remove volumes (this will delete your database data):
```bash
docker-compose down -v
# or with podman:
podman-compose down -v
```
