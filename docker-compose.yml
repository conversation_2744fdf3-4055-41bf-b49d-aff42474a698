version: '3.8'

services:
  # Next.js Application
  app:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: presentation-ai-app
    ports:
      - "3000:3000"
    environment:
      # Database - using host PostgreSQL
      DATABASE_URL: "postgresql://postgres:<EMAIL>:5432/presentation_ai"

      # Node Environment
      NODE_ENV: production

      # NextAuth
      NEXTAUTH_URL: "http://localhost:3000"
      NEXTAUTH_SECRET: "your-nextauth-secret-here"

      # API Keys (you'll need to set these)
      OPENAI_API_KEY: "your-openai-api-key"
      TOGETHER_AI_API_KEY: "your-together-ai-api-key"

      # Google OAuth (you'll need to set these)
      GOOGLE_CLIENT_ID: "your-google-client-id"
      GOOGLE_CLIENT_SECRET: "your-google-client-secret"

      # Skip env validation for Docker
      SKIP_ENV_VALIDATION: "1"
    extra_hosts:
      - "host.containers.internal:host-gateway"
    command: >
      sh -c "
        npx prisma db push &&
        node server.js
      "
