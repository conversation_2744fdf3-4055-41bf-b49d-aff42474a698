version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: presentation-ai-db
    environment:
      POSTGRES_DB: presentation_ai
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Next.js Application
  app:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: presentation-ai-app
    ports:
      - "3000:3000"
    environment:
      # Database
      DATABASE_URL: "********************************************/presentation_ai"
      
      # Node Environment
      NODE_ENV: production
      
      # NextAuth
      NEXTAUTH_URL: "http://localhost:3000"
      NEXTAUTH_SECRET: "your-nextauth-secret-here"
      
      # API Keys (you'll need to set these)
      OPENAI_API_KEY: "your-openai-api-key"
      TOGETHER_AI_API_KEY: "your-together-ai-api-key"
      
      # Google OAuth (you'll need to set these)
      GOOGLE_CLIENT_ID: "your-google-client-id"
      GOOGLE_CLIENT_SECRET: "your-google-client-secret"
      
      # Skip env validation for Docker
      SKIP_ENV_VALIDATION: "1"
    depends_on:
      postgres:
        condition: service_healthy
    command: >
      sh -c "
        npx prisma db push &&
        node server.js
      "

volumes:
  postgres_data:
