# Database
DATABASE_URL="postgresql://postgres:postgres@localhost:5432/presentation_ai"

# Node Environment
NODE_ENV="development"

# NextAuth Configuration
NEXTAUTH_URL="http://localhost:3000"
NEXTAUTH_SECRET="your-nextauth-secret-here-generate-a-random-string"

# OpenAI API Key
OPENAI_API_KEY="********************************************************************************************************************************************************************"

# Together AI API Key
TOGETHER_AI_API_KEY="your-together-ai-api-key-here"

# Google OAuth Credentials
GOOGLE_CLIENT_ID="your-google-client-id-here"
GOOGLE_CLIENT_SECRET="your-google-client-secret-here"

# Skip environment validation (useful for Docker builds)
# SKIP_ENV_VALIDATION="1"
