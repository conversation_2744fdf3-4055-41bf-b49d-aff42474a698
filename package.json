{"name": "presentation", "version": "0.1.0", "private": true, "type": "module", "scripts": {"build": "next build", "db:push": "prisma db push", "db:studio": "prisma studio", "dev": "next dev", "postinstall": "prisma generate", "lint": "next lint", "start": "next start"}, "dependencies": {"@ai-sdk/openai": "^1.1.5", "@ai-sdk/react": "^0.0.6", "@ariakit/react": "^0.4.15", "@auth/prisma-adapter": "^1.6.0", "@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@fortawesome/fontawesome-svg-core": "^6.7.2", "@fortawesome/free-brands-svg-icons": "^6.7.2", "@fortawesome/free-regular-svg-icons": "^6.7.2", "@fortawesome/free-solid-svg-icons": "^6.7.2", "@fortawesome/react-fontawesome": "^0.2.2", "@hookform/resolvers": "^3.10.0", "@langchain/core": "0.3.36", "@langchain/openai": "^0.4.2", "@prisma/client": "^5.22.0", "@radix-ui/react-accordion": "^1.2.2", "@radix-ui/react-alert-dialog": "^1.1.5", "@radix-ui/react-aspect-ratio": "^1.1.1", "@radix-ui/react-avatar": "^1.1.2", "@radix-ui/react-checkbox": "^1.1.3", "@radix-ui/react-collapsible": "^1.1.2", "@radix-ui/react-context-menu": "^2.2.5", "@radix-ui/react-dialog": "^1.1.5", "@radix-ui/react-dropdown-menu": "^2.1.5", "@radix-ui/react-hover-card": "^1.1.5", "@radix-ui/react-label": "^2.1.1", "@radix-ui/react-menubar": "^1.1.5", "@radix-ui/react-navigation-menu": "^1.2.4", "@radix-ui/react-popover": "^1.1.5", "@radix-ui/react-progress": "^1.1.1", "@radix-ui/react-radio-group": "^1.2.2", "@radix-ui/react-scroll-area": "^1.2.2", "@radix-ui/react-select": "^2.1.5", "@radix-ui/react-separator": "^1.1.1", "@radix-ui/react-slider": "^1.2.2", "@radix-ui/react-slot": "^1.1.1", "@radix-ui/react-switch": "^1.1.2", "@radix-ui/react-tabs": "^1.1.2", "@radix-ui/react-toast": "^1.2.5", "@radix-ui/react-toggle": "^1.1.1", "@radix-ui/react-toggle-group": "^1.1.1", "@radix-ui/react-toolbar": "^1.1.1", "@radix-ui/react-tooltip": "^1.1.7", "@t3-oss/env-nextjs": "^0.10.1", "@tanstack/react-query": "^5.65.1", "@types/lodash.debounce": "^4.0.9", "@types/prismjs": "^1.26.5", "@udecode/cmdk": "^0.1.1", "@udecode/cn": "^40.2.8", "@udecode/plate": "^41.0.14", "@udecode/plate-ai": "^41.0.14", "@udecode/plate-alignment": "^41.0.0", "@udecode/plate-autoformat": "^41.0.0", "@udecode/plate-basic-elements": "^41.0.0", "@udecode/plate-basic-marks": "^41.0.0", "@udecode/plate-block-quote": "^41.0.0", "@udecode/plate-break": "^41.0.0", "@udecode/plate-callout": "^41.0.0", "@udecode/plate-caption": "^41.0.0", "@udecode/plate-code-block": "^41.0.0", "@udecode/plate-combobox": "^41.0.0", "@udecode/plate-comments": "^41.0.0", "@udecode/plate-common": "^41.0.13", "@udecode/plate-core": "^41.0.13", "@udecode/plate-date": "^41.0.0", "@udecode/plate-dnd": "^41.0.2", "@udecode/plate-emoji": "^41.0.0", "@udecode/plate-excalidraw": "^41.0.0", "@udecode/plate-floating": "^41.0.0", "@udecode/plate-font": "^41.0.12", "@udecode/plate-heading": "^41.0.0", "@udecode/plate-highlight": "^41.0.0", "@udecode/plate-horizontal-rule": "^41.0.0", "@udecode/plate-indent": "^41.0.0", "@udecode/plate-indent-list": "^41.0.10", "@udecode/plate-juice": "^41.0.0", "@udecode/plate-kbd": "^41.0.0", "@udecode/plate-layout": "^41.0.2", "@udecode/plate-line-height": "^41.0.0", "@udecode/plate-link": "^41.0.0", "@udecode/plate-list": "^41.0.0", "@udecode/plate-markdown": "^41.0.14", "@udecode/plate-math": "^41.0.11", "@udecode/plate-media": "^41.0.0", "@udecode/plate-mention": "^41.0.0", "@udecode/plate-node-id": "^41.0.0", "@udecode/plate-reset-node": "^41.0.0", "@udecode/plate-resizable": "^41.0.0", "@udecode/plate-select": "^41.0.0", "@udecode/plate-selection": "^41.0.8", "@udecode/plate-slash-command": "^41.0.0", "@udecode/plate-tabbable": "^41.0.0", "@udecode/plate-table": "^41.0.9", "@udecode/plate-toggle": "^41.0.0", "@udecode/plate-trailing-block": "^41.0.0", "@uploadthing/react": "^7.1.5", "ai": "^4.1.10", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.0.4", "date-fns": "^3.6.0", "embla-carousel-react": "^8.5.2", "input-otp": "^1.4.2", "langchain": "^0.3.14", "lodash.debounce": "^4.0.8", "lucide-react": "^0.379.0", "nanoid": "^5.0.9", "next": "14.2.23", "next-auth": "5.0.0-beta.19", "next-themes": "^0.3.0", "prisma": "^5.22.0", "prismjs": "^1.29.0", "prosemirror-commands": "^1.6.2", "prosemirror-history": "^1.4.1", "prosemirror-keymap": "^1.2.2", "prosemirror-markdown": "^1.13.1", "prosemirror-model": "^1.24.1", "prosemirror-schema-basic": "^1.2.3", "prosemirror-schema-list": "^1.5.0", "prosemirror-state": "^1.4.3", "prosemirror-view": "^1.38.0", "re-resizable": "^6.10.3", "react": "18.2.0", "react-colorful": "^5.6.1", "react-day-picker": "^8.10.1", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-dom": "18.2.0", "react-dropzone": "^14.3.5", "react-file-picker": "^0.0.6", "react-fontpicker-ts": "^1.2.0", "react-hook-form": "^7.54.2", "react-icons": "^5.5.0", "react-icons-picker": "^1.0.9", "react-intersection-observer": "^9.15.1", "react-resizable-panels": "^2.1.7", "recharts": "^2.15.1", "slate": "^0.103.0", "slate-react": "^0.110.3", "sonner": "^1.7.2", "tailwind-merge": "^2.6.0", "together-ai": "^0.7.0", "uploadthing": "^7.4.4", "use-file-picker": "^2.1.2", "vaul": "^0.9.9", "zod": "^3.24.1", "zustand": "^4.5.6"}, "devDependencies": {"@tailwindcss/container-queries": "^0.1.1", "@tailwindcss/typography": "^0.5.16", "@types/eslint": "^8.56.12", "@types/lodash.debounce": "^4.0.9", "@types/node": "^20.17.16", "@types/react": "^18.3.18", "@types/react-dom": "^18.3.5", "@types/webpack": "^5.28.5", "@typescript-eslint/eslint-plugin": "^7.18.0", "@typescript-eslint/parser": "^7.18.0", "eslint": "^8.57.1", "eslint-config-next": "^14.2.23", "postcss": "^8.5.1", "prettier": "^3.4.2", "prettier-plugin-tailwindcss": "^0.5.14", "prisma": "^5.22.0", "tailwind-scrollbar-hide": "^2.0.0", "tailwindcss": "^3.4.17", "tailwindcss-animate": "^1.0.7", "tailwindcss-scrollbar": "^0.1.0", "ts-node": "^10.9.2", "typescript": "^5.7.3", "webpack": "^5.97.1"}}